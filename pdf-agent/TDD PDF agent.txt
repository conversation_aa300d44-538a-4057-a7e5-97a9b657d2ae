

# **Technical Design Document: Document & Template Upload with AI Agent Workflow (Python Backend + TypeScript Frontend)**

## **Project Overview**

This project is a lightweight application with three core features:

1. **Document Upload Portal** – Users upload documents. The backend extracts text asynchronously. Job status is visible in real time.
2. **Template Upload Portal** – Users upload blank PDF templates for later use. No processing is required.
3. **AI Agent Interaction Platform** – A chat interface allowing users to run an AI agent workflow to extract data from documents and fill templates. Workflow steps are streamed in real time.

---

## **System Architecture**

### **1. Backend (Python/FastAPI)**

**Responsibilities:**

* Handle file uploads (documents & templates)
* Run asynchronous jobs for text extraction
* Expose REST APIs and WebSocket endpoints for frontend interaction
* Run AI agent workflow (Claude Code SDK + custom tools)

**Components:**

1. **File Management** – Save uploaded files in local storage or S3
2. **Job Management** – Track document processing jobs (`uploading → processing → done/failed`)
3. **Agent Workflow** – Trigger Claude Code SDK workflow and stream step outputs via WebSocket

**Data Storage:**

* Temporary local storage or cloud (S3)
* In-memory (dict) or Redis for job metadata

---

### **2. Frontend (TypeScript + React)**

**Responsibilities:**

* Document upload page with real-time job status
* Template upload page with simple confirmation
* AI agent chat interface displaying each workflow step in real time

**Integration Patterns:**

* REST API calls for file uploads and job status checks
* WebSocket for streaming AI workflow steps

---

## **Feature 1: Document Upload Portal**

**Frontend Workflow:**

1. User selects a document and clicks **Upload**.
2. Frontend sends a `POST /api/documents` request with the file.
3. Receives `job_id` in response.
4. Polls `GET /api/documents/{job_id}` every 2–3 seconds to update status:

   * `uploading` → `processing` → `done` / `failed`
5. Displays extracted text when job completes.

**Backend API:**

* `POST /api/documents`

  * Request: Multipart/form-data, file
  * Response: `{ job_id: string, status: string }`
* `GET /api/documents/{job_id}`

  * Response: `{ job_id: string, status: string, result?: string }`

**Backend Implementation:**

* Use **FastAPI** `BackgroundTasks` or **Celery** for async processing
* Store extracted text in memory/Redis

---

## **Feature 2: Template Upload Portal**

**Frontend Workflow:**

1. User selects a PDF template and clicks **Upload**.
2. Frontend sends `POST /api/templates` request with the file.
3. Displays confirmation message when upload succeeds.

**Backend API:**

* `POST /api/templates`

  * Request: Multipart/form-data, file
  * Response: `{ filename: string, message: string }`

**Storage:**

* `/uploads/templates/<filename>` or S3

---

## **Feature 3: AI Agent Interaction Platform**

**Frontend Workflow:**

1. User selects one or more documents and a template.
2. Clicks **Start Workflow**.
3. Frontend opens a **WebSocket** connection to backend at `/ws/agent-workflow/{workflow_id}`.
4. Backend streams messages for each workflow step:

   ```json
   {
     "step_id": "uuid",
     "message": "Current step description",
     "status": "processing | done",
     "timestamp": "ISO8601"
   }
   ```
5. Frontend appends messages to chat window in real time.

**Backend WebSocket Endpoint:**

* `ws://<backend>/ws/agent-workflow/{workflow_id}`

  * On connect, triggers Claude Code SDK workflow
  * Streams each step as JSON message

**Optional REST API to trigger workflow (without streaming):**

* `POST /api/agent-workflow`

  * Request: `{ documents: string[], template: string }`
  * Response: `{ workflow_id: string }`

---

## **File Storage Structure**

```
/uploads/documents/
    └─ <job_id>_<original_filename>
/uploads/templates/
    └─ <original_filename>
/outputs/
    └─ <filled_template_job_id>.pdf
```

---

## **Data Models**

**Job Model (Document Extraction):**

```ts
interface Job {
  job_id: string;
  type: "document_extraction";
  status: "uploading" | "processing" | "done" | "failed";
  result?: string;
}
```

**Template Model:**

```ts
interface Template {
  filename: string;
  path: string;
}
```

**AI Workflow Step Model:**

```ts
interface WorkflowStep {
  step_id: string;
  message: string;
  status: "processing" | "done";
  timestamp: string;
}
```

---

## **Frontend Pages**

1. **Document Upload Page**

   * File input + Upload button
   * List of files with status (uploading → processing → done/failed)

2. **Template Upload Page**

   * File input + Upload button
   * Confirmation message

3. **AI Agent Workflow Page**

   * Multi-select documents
   * Template dropdown
   * Start Workflow button
   * Chat window showing workflow steps in real time

---

## **Real-Time Updates**

**Document Processing:**

* Polling via `GET /api/documents/{job_id}`

**AI Agent Workflow:**

* WebSocket streaming messages for each step

---

## **Security Considerations**

* Limit upload size (e.g., 50 MB)
* Restrict file types (PDF, DOCX, TXT)
* Sanitize filenames
* Optional authentication / API key

---

## **Optional Enhancements**

* Persist workflow outputs and extracted text for download
* Dashboard for tracking multiple uploads and workflows
* Error reporting and retry mechanism
* Use cloud storage (S3/GCS) for scalability

---

✅ **Summary**

* **Backend:** Python/FastAPI for file management, async jobs, and AI agent workflow
* **Frontend:** TypeScript/React for modern UI, real-time updates, and chat interface
* **Integration:** REST API for file uploads and job status, WebSocket for agent workflow streaming
* **Benefits:** Leverages existing Python agent workflow, strong typing in frontend, modular architecture

