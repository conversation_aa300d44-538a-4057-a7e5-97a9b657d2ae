
import asyncio
from claude_code_sdk import query, ClaudeCodeO<PERSON><PERSON>, create_sdk_mcp_server, tool, ClaudeSDKClient
import json
from pypdf import Pdf<PERSON>ead<PERSON>, PdfWriter

@tool("get_text_from_pdf", "Extract text from a PDF", {"path": str})
async def get_text_from_pdf(args):
    reader = PdfReader(args["path"])
    text = ""
    for page in reader.pages:
        text += page.extract_text()
    return {
        "content": [{
            "type": "text",
            "text": text
        }]
    }

@tool("list_pdfs", "Lists PDFs in a directory", {"path": str})
async def list_pdfs(args):
    import os
    return {
        "content": [{
            "type": "text",
            "text": json.dumps(os.listdir(args["path"]))
        }]
    }

@tool("get_form_fields", "Extract form fields from a PDF", {"path": str})
async def get_form_fields(args):
    reader = PdfReader(args["path"])
    fields = {}
    
    if "/AcroForm" in reader.trailer["/Root"]:
        form = reader.trailer["/Root"]["/AcroForm"]
        form_fields = form.get("/Fields", [])
        for field in form_fields:
            field_obj = field.get_object()
            name = field_obj.get("/T")
            if not name:
                continue

            # Get the field type
            field_type = field_obj.get("/FT")  # /Tx (text), /Btn (button/checkbox), /Ch (choice)
            if field_type == "/Tx":
                type_str = "text"
            elif field_type == "/Btn":
                type_str = "button"
            elif field_type == "/Ch":
                type_str = "choice"
            else:
                type_str = "unknown"

            # Get the current value
            value = field_obj.get("/V", "")

            fields[name] = {
                "type": type_str,
                "value": value
            }

    return {
        "content": [{
            "type": "text",
            "text": json.dumps(fields, indent=2)
        }]
    }

@tool("fill_form_fields", "Fill a PDF form with given field values", {"path": str, "field_values": str, "output_path": str})
async def fill_form_fields(args):
    """
    Fill PDF form fields with given values.
    
    Args:
        input_path: Path to the input PDF with AcroForm fields.
        field_values: Dict mapping field names to values to fill.
                     Can be either flat dict {"field": "value"} or nested dict {"field": {"value": "value"}}
        output_path: Path to save the filled PDF.
        
    Returns:
        dict: Success message
    """
    input_path = args["path"]
    field_values = json.loads(args["field_values"])
    output_path = args["output_path"]

    reader = PdfReader(input_path)
    writer = PdfWriter()

    # Copy all pages
    writer.append(reader)

    # Prepare field values - handle both flat and nested structures
    flat_field_values = {}
    for field_name, field_data in field_values.items():
        if isinstance(field_data, dict) and "value" in field_data:
            # Nested structure: {"field": {"type": "text", "value": "actual_value"}}
            flat_field_values[field_name] = field_data["value"]
        else:
            # Flat structure: {"field": "actual_value"}
            flat_field_values[field_name] = field_data

    # Use the recommended pypdf method for updating form fields
    try:
        writer.update_page_form_field_values(
            writer.pages[0], 
            flat_field_values,
            auto_regenerate=False
        )
    except Exception as e:
        print(f"Warning: Could not update some fields: {e}")
        # Fallback: try to update fields individually
        for field_name, value in flat_field_values.items():
            try:
                writer.update_page_form_field_values(
                    writer.pages[0], 
                    {field_name: value},
                    auto_regenerate=False
                )
            except Exception as field_error:
                print(f"Could not update field '{field_name}': {field_error}")

    # Write filled PDF
    with open(output_path, "wb") as f_out:
        writer.write(f_out)

    return {
        "content": [{
            "type": "text",
            "text": f"Form filled and saved to {output_path}"
        }]
    }



forms = create_sdk_mcp_server(
    name="forms",
    version="1.0.0",
    tools=[get_form_fields, fill_form_fields, get_text_from_pdf, list_pdfs]  # Pass decorated functions
)

# Use with Claude
options = ClaudeCodeOptions(
    mcp_servers={"forms": forms},
    allowed_tools=["Read", "Write", "Edit","mcp__forms__get_form_fields", "mcp__forms__fill_form_fields", "mcp__forms__get_text_from_pdf", "mcp__forms__list_pdfs"],
)


pdf_input_path="input",
form_path="forms/job-app-form-EDITED.pdf",
output_path="filled.pdf"

prompt = f"""
Extract the form fields from the following PDF:

{form_path}

Then, fill out the form with information from documents in the following directory:

{pdf_input_path}

Save the filled form to {output_path}.

Form filling information:
The fields argument for fill_form_fields should be a dictionary mapping field names to values. 
The field names are the keys in the dictionary returned by get_form_fields as json.dumps output. The values are the values to fill in the form.

If the type of the field is 'text', the value should be a string.
If the type of the field is 'button', the value should be the name of the button to select. For example, if the button name is 'YES GRADUATED', the value should be '/YES GRADUATED'.

Sample input for fill_form_fields:
'Zip Code': {{'type': 'text', 'value': '62704'}},
'YES GRADUATED': {{'type': 'button', 'value': '/YES GRADUATED'}},
'NO GRADUATED': {{'type': 'button', 'value': ''}}

Only include fields that have a value.

"""

async with ClaudeSDKClient(options=options) as client:
    await client.query(prompt)

    # Extract and print response
    async for msg in client.receive_response():
        print(msg)
