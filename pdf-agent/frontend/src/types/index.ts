/**
 * TypeScript type definitions for the PDF Agent application.
 * These match the backend models defined in the TDD.
 */

// Job-related types
export type JobStatus = "uploading" | "processing" | "done" | "failed";
export type JobType = "document_extraction" | "template_processing" | "agent_workflow";

export interface Job {
  job_id: string;
  type: JobType;
  status: JobStatus;
  filename?: string;
  result?: string;
  error?: string;
  created_at: string;
  updated_at?: string;
}

// Template-related types
export interface Template {
  filename: string;
  path: string;
  uploaded_at: string;
  file_size?: number;
  form_fields?: Record<string, any>;
}

// Workflow-related types
export type WorkflowStepStatus = "processing" | "done" | "error";

export interface WorkflowStep {
  step_id: string;
  message: string;
  status: WorkflowStepStatus;
  timestamp: string;
  data?: Record<string, any>;
  error?: string;
}

// API Request/Response types
export interface DocumentUploadResponse {
  job_id: string;
  status: string;
  message?: string;
}

export interface TemplateUploadResponse {
  filename: string;
  message: string;
  path: string;
}

export interface JobStatusResponse {
  job_id: string;
  status: string;
  filename?: string;
  created_at: string;
  result?: string;
  error?: string;
}

export interface AgentWorkflowRequest {
  documents: string[];
  template: string;
  options?: Record<string, any>;
}

export interface AgentWorkflowResponse {
  workflow_id: string;
  status: string;
  message: string;
}

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
  services: Record<string, string>;
}

// UI-specific types
export interface FileUploadState {
  file: File | null;
  uploading: boolean;
  progress: number;
  error: string | null;
  success: boolean;
}

export interface DocumentListItem {
  job_id: string;
  filename: string;
  status: JobStatus;
  created_at: string;
  result?: string;
  error?: string;
}

export interface TemplateListItem {
  filename: string;
  uploaded_at: string;
  file_size?: number;
}

export interface WorkflowConfig {
  selectedDocuments: string[];
  selectedTemplate: string;
  isRunning: boolean;
  workflowId?: string;
}

// WebSocket message types
export interface WebSocketMessage {
  type: 'workflow_step' | 'error' | 'complete';
  data: WorkflowStep | { error: string } | { workflow_id: string };
}

// API Error type
export interface APIError {
  error: string;
  detail?: string;
  timestamp: string;
}

// Component prop types
export interface FileUploaderProps {
  accept: string;
  multiple?: boolean;
  onUpload: (files: FileList) => void;
  uploading?: boolean;
  disabled?: boolean;
}

export interface JobStatusProps {
  job: Job;
  onRefresh?: () => void;
}

export interface ChatInterfaceProps {
  steps: WorkflowStep[];
  isRunning: boolean;
  onStart: (config: WorkflowConfig) => void;
  onStop: () => void;
}

// Service types
export interface APIClient {
  uploadDocument: (file: File) => Promise<DocumentUploadResponse>;
  getDocumentStatus: (jobId: string) => Promise<JobStatusResponse>;
  listDocuments: () => Promise<DocumentListItem[]>;
  uploadTemplate: (file: File) => Promise<TemplateUploadResponse>;
  listTemplates: () => Promise<TemplateListItem[]>;
  startWorkflow: (request: AgentWorkflowRequest) => Promise<AgentWorkflowResponse>;
  healthCheck: () => Promise<HealthCheckResponse>;
}

export interface WebSocketClient {
  connect: (workflowId: string) => void;
  disconnect: () => void;
  send: (message: any) => void;
  onMessage: (callback: (message: WebSocketMessage) => void) => void;
  onError: (callback: (error: Event) => void) => void;
  onClose: (callback: (event: CloseEvent) => void) => void;
}
