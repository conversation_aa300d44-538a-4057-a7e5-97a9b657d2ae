.template-upload {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h2 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 2rem;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

.error-banner, .success-banner {
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.error-banner {
  background: #ffebee;
  border: 1px solid #f44336;
  color: #c62828;
}

.success-banner {
  background: #e8f5e9;
  border: 1px solid #4caf50;
  color: #2e7d32;
}

.error-banner button, .success-banner button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0.25rem;
  border-radius: 4px;
}

.error-banner button {
  color: #c62828;
}

.success-banner button {
  color: #2e7d32;
}

.error-banner button:hover {
  background: rgba(244, 67, 54, 0.1);
}

.success-banner button:hover {
  background: rgba(76, 175, 80, 0.1);
}

.upload-section {
  margin-bottom: 3rem;
}

.upload-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.info-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
}

.info-card h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.info-card ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
}

.info-card li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.templates-section {
  margin-top: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #eee;
}

.section-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.refresh-btn {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #666;
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: #e9e9e9;
  border-color: #ccc;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 3rem;
  color: #666;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #646cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  color: #333;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.5;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .template-upload {
    padding: 0 1rem;
  }
  
  .page-header h2 {
    font-size: 1.5rem;
  }
  
  .upload-info {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .info-card {
    padding: 1rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .error-banner, .success-banner {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .error-banner button, .success-banner button {
    align-self: flex-end;
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .info-card h4 {
    font-size: 1rem;
  }
  
  .info-card {
    padding: 0.75rem;
  }
  
  .empty-state {
    padding: 2rem 1rem;
  }
  
  .empty-icon {
    font-size: 3rem;
  }
}
