/**
 * Template Upload Page
 * Allows users to upload PDF templates for form filling.
 */

import React, { useState, useEffect } from 'react';
import { apiClient } from '../services/api';
import { TemplateListItem } from '../types';
import FileUploader from '../components/FileUploader';
import TemplateCard from '../components/TemplateCard';
import './TemplateUpload.css';

const TemplateUpload: React.FC = () => {
  const [templates, setTemplates] = useState<TemplateListItem[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const templateList = await apiClient.listTemplates();
      setTemplates(templateList);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (files: FileList) => {
    if (files.length === 0) return;

    setUploading(true);
    setError(null);
    setSuccess(null);

    try {
      // Upload files one by one (templates are typically uploaded individually)
      const uploadPromises = Array.from(files).map(file => 
        apiClient.uploadTemplate(file)
      );

      const results = await Promise.all(uploadPromises);
      
      // Show success message
      const fileNames = results.map(r => r.filename).join(', ');
      setSuccess(`Successfully uploaded: ${fileNames}`);
      
      // Reload templates list
      await loadTemplates();
      
      // Clear success message after 5 seconds
      setTimeout(() => setSuccess(null), 5000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const handleRefresh = () => {
    loadTemplates();
  };

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return 'Unknown size';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="template-upload">
      <div className="page-header">
        <h2>Template Upload</h2>
        <p>Upload PDF templates for form filling with AI agent</p>
      </div>

      {error && (
        <div className="error-banner">
          <span>❌</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      {success && (
        <div className="success-banner">
          <span>✅</span>
          <span>{success}</span>
          <button onClick={() => setSuccess(null)}>×</button>
        </div>
      )}

      <div className="upload-section">
        <FileUploader
          accept=".pdf"
          multiple={true}
          onUpload={handleFileUpload}
          uploading={uploading}
          disabled={uploading}
        />
        
        <div className="upload-info">
          <div className="info-card">
            <h4>📋 Template Requirements</h4>
            <ul>
              <li>Only PDF files are supported</li>
              <li>Templates should contain fillable form fields</li>
              <li>Maximum file size: 50MB</li>
              <li>Ensure form fields have clear, descriptive names</li>
            </ul>
          </div>
          
          <div className="info-card">
            <h4>💡 Tips for Best Results</h4>
            <ul>
              <li>Use descriptive field names (e.g., "firstName", "email", "address")</li>
              <li>Test your template in a PDF viewer before uploading</li>
              <li>Avoid complex nested forms or unusual field types</li>
              <li>Consider using standard field names for common data</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="templates-section">
        <div className="section-header">
          <h3>Available Templates ({templates.length})</h3>
          <button 
            className="refresh-btn"
            onClick={handleRefresh}
            disabled={loading}
          >
            🔄 Refresh
          </button>
        </div>

        {loading ? (
          <div className="loading">
            <div className="spinner"></div>
            <span>Loading templates...</span>
          </div>
        ) : templates.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📄</div>
            <h4>No templates uploaded yet</h4>
            <p>Upload your first PDF template to get started with AI form filling</p>
          </div>
        ) : (
          <div className="templates-grid">
            {templates.map(template => (
              <TemplateCard
                key={template.filename}
                template={template}
                onRefresh={handleRefresh}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateUpload;
