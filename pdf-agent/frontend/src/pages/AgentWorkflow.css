.agent-workflow {
  max-width: 1400px;
  margin: 0 auto;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 2rem;
  flex-shrink: 0;
}

.page-header h2 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 2rem;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

.error-banner {
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #c62828;
  flex-shrink: 0;
}

.error-banner button {
  background: none;
  border: none;
  color: #c62828;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0.25rem;
  border-radius: 4px;
}

.error-banner button:hover {
  background: rgba(244, 67, 54, 0.1);
}

.workflow-container {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 2rem;
  flex: 1;
  min-height: 0;
}

.workflow-sidebar {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  overflow-y: auto;
  max-height: 100%;
}

.selection-section {
  background: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.selection-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.workflow-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.start-btn, .stop-btn, .refresh-btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.start-btn.ready {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.start-btn.ready:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.start-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.stop-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.stop-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
}

.refresh-btn {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.refresh-btn:hover:not(:disabled) {
  background: #e9e9e9;
  border-color: #ccc;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.workflow-info {
  background: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  margin-top: auto;
}

.workflow-info h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.workflow-info ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.workflow-info li {
  margin-bottom: 0.5rem;
}

.workflow-main {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* Responsive design */
@media (max-width: 1024px) {
  .workflow-container {
    grid-template-columns: 300px 1fr;
    gap: 1.5rem;
  }
  
  .workflow-sidebar {
    padding: 1.25rem;
  }
  
  .selection-section {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .agent-workflow {
    height: auto;
    min-height: calc(100vh - 200px);
  }
  
  .workflow-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .workflow-sidebar {
    order: 2;
    max-height: none;
  }
  
  .workflow-main {
    order: 1;
    min-height: 400px;
  }
  
  .page-header h2 {
    font-size: 1.5rem;
  }
  
  .error-banner {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .error-banner button {
    align-self: flex-end;
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .agent-workflow {
    padding: 0 1rem;
  }
  
  .workflow-sidebar {
    padding: 1rem;
    gap: 1rem;
  }
  
  .selection-section {
    padding: 0.75rem;
  }
  
  .workflow-controls {
    gap: 0.5rem;
  }
  
  .start-btn, .stop-btn, .refresh-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
  }
}

/* Animation for workflow running state */
.workflow-container.running .workflow-sidebar {
  border-left: 3px solid #2196f3;
  animation: pulse-border 2s ease-in-out infinite;
}

@keyframes pulse-border {
  0%, 100% {
    border-left-color: #2196f3;
  }
  50% {
    border-left-color: #64b5f6;
  }
}
