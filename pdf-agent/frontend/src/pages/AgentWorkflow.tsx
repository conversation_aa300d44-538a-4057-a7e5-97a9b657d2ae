/**
 * AI Agent Workflow Page
 * Provides interface for running AI agent workflow with real-time streaming.
 */

import React, { useState, useEffect, useRef } from 'react';
import { apiClient } from '../services/api';
import { useWebSocket } from '../services/websocket';
import { 
  DocumentListItem, 
  TemplateListItem, 
  WorkflowStep, 
  WebSocketMessage,
  WorkflowConfig 
} from '../types';
import WorkflowChat from '../components/WorkflowChat';
import DocumentSelector from '../components/DocumentSelector';
import TemplateSelector from '../components/TemplateSelector';
import './AgentWorkflow.css';

const AgentWorkflow: React.FC = () => {
  const [documents, setDocuments] = useState<DocumentListItem[]>([]);
  const [templates, setTemplates] = useState<TemplateListItem[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // WebSocket connection
  const { connect, disconnect, send } = useWebSocket(
    currentWorkflowId,
    handleWebSocketMessage,
    handleWebSocketError,
    handleWebSocketClose
  );

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Cleanup WebSocket on unmount
  useEffect(() => {
    return () => {
      if (currentWorkflowId) {
        disconnect();
      }
    };
  }, [currentWorkflowId, disconnect]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [docsData, templatesData] = await Promise.all([
        apiClient.listDocuments(),
        apiClient.listTemplates()
      ]);
      
      // Only show completed documents
      const completedDocs = docsData.filter(doc => doc.status === 'done');
      setDocuments(completedDocs);
      setTemplates(templatesData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  function handleWebSocketMessage(message: WebSocketMessage) {
    console.log('WebSocket message:', message);
    
    if (message.type === 'workflow_step') {
      const step = message.data as WorkflowStep;
      setWorkflowSteps(prev => [...prev, step]);
    } else if (message.type === 'error') {
      const errorData = message.data as { error: string };
      setError(errorData.error);
      setIsRunning(false);
    } else if (message.type === 'complete') {
      setIsRunning(false);
    }
  }

  function handleWebSocketError(error: Event) {
    console.error('WebSocket error:', error);
    setError('Connection error occurred');
    setIsRunning(false);
  }

  function handleWebSocketClose(event: CloseEvent) {
    console.log('WebSocket closed:', event);
    if (event.code !== 1000) { // Not a normal closure
      setError('Connection lost');
    }
    setIsRunning(false);
  }

  const handleStartWorkflow = async () => {
    if (selectedDocuments.length === 0) {
      setError('Please select at least one document');
      return;
    }
    
    if (!selectedTemplate) {
      setError('Please select a template');
      return;
    }

    try {
      setError(null);
      setWorkflowSteps([]);
      setIsRunning(true);

      // Generate workflow ID
      const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setCurrentWorkflowId(workflowId);

      // Connect to WebSocket
      connect();

      // Send workflow configuration
      const config = {
        documents: selectedDocuments,
        template: selectedTemplate,
        options: {}
      };

      // Wait a moment for connection to establish
      setTimeout(() => {
        send(config);
      }, 500);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start workflow');
      setIsRunning(false);
    }
  };

  const handleStopWorkflow = () => {
    if (currentWorkflowId) {
      disconnect();
      setCurrentWorkflowId(null);
    }
    setIsRunning(false);
  };

  const handleRefresh = () => {
    loadData();
  };

  const canStartWorkflow = () => {
    return !loading && 
           !isRunning && 
           selectedDocuments.length > 0 && 
           selectedTemplate !== '';
  };

  return (
    <div className="agent-workflow">
      <div className="page-header">
        <h2>AI Agent Workflow</h2>
        <p>Select documents and a template to run the AI form filling workflow</p>
      </div>

      {error && (
        <div className="error-banner">
          <span>❌</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="workflow-container">
        <div className="workflow-sidebar">
          <div className="selection-section">
            <h3>📄 Select Documents</h3>
            <DocumentSelector
              documents={documents}
              selectedDocuments={selectedDocuments}
              onSelectionChange={setSelectedDocuments}
              disabled={isRunning || loading}
            />
          </div>

          <div className="selection-section">
            <h3>📋 Select Template</h3>
            <TemplateSelector
              templates={templates}
              selectedTemplate={selectedTemplate}
              onSelectionChange={setSelectedTemplate}
              disabled={isRunning || loading}
            />
          </div>

          <div className="workflow-controls">
            <button
              className={`start-btn ${canStartWorkflow() ? 'ready' : 'disabled'}`}
              onClick={handleStartWorkflow}
              disabled={!canStartWorkflow()}
            >
              {isRunning ? '⏳ Running...' : '🚀 Start Workflow'}
            </button>

            {isRunning && (
              <button
                className="stop-btn"
                onClick={handleStopWorkflow}
              >
                ⏹️ Stop Workflow
              </button>
            )}

            <button
              className="refresh-btn"
              onClick={handleRefresh}
              disabled={loading || isRunning}
            >
              🔄 Refresh Data
            </button>
          </div>

          <div className="workflow-info">
            <h4>ℹ️ How it works</h4>
            <ol>
              <li>Select one or more processed documents</li>
              <li>Choose a PDF template with form fields</li>
              <li>Click "Start Workflow" to begin</li>
              <li>Watch the AI extract data and fill the form</li>
              <li>Download the completed form when done</li>
            </ol>
          </div>
        </div>

        <div className="workflow-main">
          <WorkflowChat
            steps={workflowSteps}
            isRunning={isRunning}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default AgentWorkflow;
