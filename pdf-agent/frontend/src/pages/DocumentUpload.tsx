/**
 * Document Upload Page
 * Allows users to upload documents and track processing status in real-time.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../services/api';
import { DocumentListItem, JobStatus } from '../types';
import FileUploader from '../components/FileUploader';
import JobStatusCard from '../components/JobStatusCard';
import './DocumentUpload.css';

const DocumentUpload: React.FC = () => {
  const [documents, setDocuments] = useState<DocumentListItem[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Load documents on component mount
  useEffect(() => {
    loadDocuments();
  }, []);

  // Poll for status updates of processing documents
  useEffect(() => {
    const processingDocs = documents.filter(doc => 
      doc.status === 'uploading' || doc.status === 'processing'
    );

    if (processingDocs.length === 0) return;

    const interval = setInterval(() => {
      processingDocs.forEach(doc => {
        refreshDocumentStatus(doc.job_id);
      });
    }, 2000); // Poll every 2 seconds

    return () => clearInterval(interval);
  }, [documents]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const docs = await apiClient.listDocuments();
      setDocuments(docs);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const refreshDocumentStatus = async (jobId: string) => {
    try {
      const status = await apiClient.getDocumentStatus(jobId);
      setDocuments(prev => prev.map(doc => 
        doc.job_id === jobId 
          ? { ...doc, status: status.status as JobStatus, result: status.result, error: status.error }
          : doc
      ));
    } catch (err) {
      console.error('Failed to refresh document status:', err);
    }
  };

  const handleFileUpload = async (files: FileList) => {
    if (files.length === 0) return;

    setUploading(true);
    setError(null);

    try {
      // Upload files one by one
      for (const file of Array.from(files)) {
        const response = await apiClient.uploadDocument(file);
        
        // Add new document to the list
        const newDoc: DocumentListItem = {
          job_id: response.job_id,
          filename: file.name,
          status: response.status as JobStatus,
          created_at: new Date().toISOString()
        };
        
        setDocuments(prev => [newDoc, ...prev]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const handleRefresh = useCallback(() => {
    loadDocuments();
  }, []);

  const getStatusColor = (status: JobStatus): string => {
    switch (status) {
      case 'uploading': return '#ffa500';
      case 'processing': return '#2196f3';
      case 'done': return '#4caf50';
      case 'failed': return '#f44336';
      default: return '#666';
    }
  };

  const getStatusIcon = (status: JobStatus): string => {
    switch (status) {
      case 'uploading': return '⬆️';
      case 'processing': return '⚙️';
      case 'done': return '✅';
      case 'failed': return '❌';
      default: return '❓';
    }
  };

  return (
    <div className="document-upload">
      <div className="page-header">
        <h2>Document Upload</h2>
        <p>Upload documents for text extraction and processing</p>
      </div>

      {error && (
        <div className="error-banner">
          <span>❌</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="upload-section">
        <FileUploader
          accept=".pdf,.docx,.txt"
          multiple={true}
          onUpload={handleFileUpload}
          uploading={uploading}
          disabled={uploading}
        />
      </div>

      <div className="documents-section">
        <div className="section-header">
          <h3>Documents ({documents.length})</h3>
          <button 
            className="refresh-btn"
            onClick={handleRefresh}
            disabled={loading}
          >
            🔄 Refresh
          </button>
        </div>

        {loading ? (
          <div className="loading">
            <div className="spinner"></div>
            <span>Loading documents...</span>
          </div>
        ) : documents.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📄</div>
            <h4>No documents uploaded yet</h4>
            <p>Upload your first document to get started</p>
          </div>
        ) : (
          <div className="documents-grid">
            {documents.map(doc => (
              <JobStatusCard
                key={doc.job_id}
                job={doc}
                onRefresh={() => refreshDocumentStatus(doc.job_id)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentUpload;
