/**
 * Template Card Component
 * Displays information about an uploaded PDF template.
 */

import React from 'react';
import { TemplateListItem } from '../types';
import './TemplateCard.css';

interface TemplateCardProps {
  template: TemplateListItem;
  onRefresh?: () => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onRefresh }) => {
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return 'Unknown size';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch {
      return dateString;
    }
  };

  const getFileIcon = (filename: string): string => {
    if (filename.toLowerCase().endsWith('.pdf')) {
      return '📄';
    }
    return '📋';
  };

  const handlePreview = () => {
    // In a real implementation, this could open a PDF preview
    // For now, we'll just show an alert
    alert(`Preview functionality for ${template.filename} would be implemented here`);
  };

  const handleDownload = () => {
    // In a real implementation, this would download the template
    // For now, we'll just show an alert
    alert(`Download functionality for ${template.filename} would be implemented here`);
  };

  return (
    <div className="template-card">
      <div className="card-header">
        <div className="template-icon">
          {getFileIcon(template.filename)}
        </div>
        <div className="template-info">
          <h4 className="template-name" title={template.filename}>
            {template.filename}
          </h4>
          <div className="template-meta">
            <span className="file-size">{formatFileSize(template.file_size)}</span>
          </div>
        </div>
      </div>

      <div className="card-content">
        <div className="template-details">
          <div className="detail-item">
            <span className="detail-label">📅 Uploaded:</span>
            <span className="detail-value">{formatDate(template.uploaded_at)}</span>
          </div>
          
          {template.file_size && (
            <div className="detail-item">
              <span className="detail-label">📏 Size:</span>
              <span className="detail-value">{formatFileSize(template.file_size)}</span>
            </div>
          )}
        </div>

        <div className="template-status">
          <div className="status-indicator ready">
            <span className="status-icon">✅</span>
            <span className="status-text">Ready for use</span>
          </div>
        </div>
      </div>

      <div className="card-actions">
        <button 
          className="action-btn preview-btn"
          onClick={handlePreview}
          title="Preview template"
        >
          👁️ Preview
        </button>
        
        <button 
          className="action-btn download-btn"
          onClick={handleDownload}
          title="Download template"
        >
          💾 Download
        </button>
        
        {onRefresh && (
          <button 
            className="action-btn refresh-btn"
            onClick={onRefresh}
            title="Refresh template info"
          >
            🔄
          </button>
        )}
      </div>
    </div>
  );
};

export default TemplateCard;
