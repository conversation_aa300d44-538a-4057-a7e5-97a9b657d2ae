.workflow-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
  flex-shrink: 0;
}

.chat-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.status-indicator.idle {
  background: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.status-indicator.running {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.status-indicator.loading {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4caf50;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.small {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.empty-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #666;
  padding: 2rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-chat h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  color: #333;
}

.empty-chat p {
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

.loading-message {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: #666;
  font-size: 1.1rem;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.step-message {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1rem;
  border-left: 4px solid #e0e0e0;
  transition: all 0.3s ease;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-message.processing {
  border-left-color: #2196f3;
  background: #f3f8ff;
}

.step-message.done {
  border-left-color: #4caf50;
  background: #f1f8e9;
}

.step-message.error {
  border-left-color: #f44336;
  background: #ffebee;
}

.step-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.step-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.step-info {
  flex: 1;
}

.step-message-text {
  color: #333;
  font-size: 1rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.step-timestamp {
  color: #666;
  font-size: 0.8rem;
  font-family: 'Courier New', monospace;
}

.processing-spinner {
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.step-data {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.data-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.data-key {
  font-weight: 600;
  color: #555;
  min-width: 100px;
}

.data-value {
  color: #333;
  font-family: 'Courier New', monospace;
  word-break: break-word;
}

.step-error {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 6px;
  color: #c62828;
  font-size: 0.9rem;
  line-height: 1.4;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f0f2ff;
  border-radius: 12px;
  border-left: 4px solid #2196f3;
  margin-top: 0.5rem;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #2196f3;
  animation: typing 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.typing-text {
  color: #1976d2;
  font-style: italic;
  font-size: 0.9rem;
}

.chat-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
  flex-shrink: 0;
}

.workflow-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #2e7d32;
  font-weight: 500;
}

.download-result-btn {
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.download-result-btn:hover {
  background: #45a049;
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-header {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .chat-header h3 {
    font-size: 1.2rem;
  }
  
  .chat-content {
    padding: 0.75rem;
  }
  
  .step-message {
    padding: 0.75rem;
  }
  
  .step-header {
    gap: 0.5rem;
  }
  
  .workflow-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .download-result-btn {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .empty-chat {
    padding: 1rem;
  }
  
  .empty-icon {
    font-size: 3rem;
  }
  
  .data-item {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .data-key {
    min-width: auto;
  }
}
