.document-selector {
  width: 100%;
}

.document-selector.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.document-selector.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.empty-state {
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.empty-state small {
  font-size: 0.85rem;
  color: #888;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.selection-info {
  font-size: 0.9rem;
  color: #666;
}

.selected-count {
  font-weight: 600;
  color: #333;
}

.select-all-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  cursor: pointer;
  font-size: 0.8rem;
  color: #666;
  transition: all 0.2s;
}

.select-all-btn:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #ccc;
  color: #333;
}

.select-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.documents-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
}

.document-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.document-item:last-child {
  border-bottom: none;
}

.document-item:hover:not(.disabled) {
  background: #f8f9fa;
}

.document-item.selected {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.document-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.document-checkbox {
  position: relative;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.document-checkbox input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 18px;
  height: 18px;
  margin: 0;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 3px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.document-item.selected .checkbox-custom {
  background: #2196f3;
  border-color: #2196f3;
}

.checkmark {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.document-info {
  flex: 1;
  min-width: 0;
}

.document-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.8rem;
}

.document-date {
  color: #666;
  font-family: 'Courier New', monospace;
}

.document-status {
  color: #2e7d32;
  font-weight: 500;
}

.document-preview {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 0.8rem;
  color: #555;
  line-height: 1.3;
  font-family: 'Courier New', monospace;
}

.preview-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.selection-summary {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.summary-header {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #333;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #2196f3;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.selected-name {
  font-weight: 500;
}

.remove-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.remove-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Scrollbar styling */
.documents-list::-webkit-scrollbar {
  width: 6px;
}

.documents-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.documents-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.documents-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
  .selector-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .documents-list {
    max-height: 250px;
  }
  
  .document-item {
    padding: 0.6rem;
    gap: 0.6rem;
  }
  
  .document-meta {
    font-size: 0.75rem;
  }
  
  .selected-list {
    gap: 0.4rem;
  }
  
  .selected-item {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .document-name {
    font-size: 0.85rem;
  }
  
  .document-preview {
    font-size: 0.75rem;
    padding: 0.4rem;
  }
  
  .selection-summary {
    padding: 0.6rem;
  }
  
  .summary-header {
    font-size: 0.85rem;
  }
}
