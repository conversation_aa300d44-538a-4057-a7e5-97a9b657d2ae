/**
 * Workflow Chat Component
 * Displays real-time workflow steps in a chat-like interface.
 */

import React, { useEffect, useRef } from 'react';
import { WorkflowStep, WorkflowStepStatus } from '../types';
import './WorkflowChat.css';

interface WorkflowChatProps {
  steps: WorkflowStep[];
  isRunning: boolean;
  loading?: boolean;
}

const WorkflowChat: React.FC<WorkflowChatProps> = ({ steps, isRunning, loading = false }) => {
  const chatEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new steps are added
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [steps]);

  const getStepIcon = (status: WorkflowStepStatus): string => {
    switch (status) {
      case 'processing': return '⚙️';
      case 'done': return '✅';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  const getStepColor = (status: WorkflowStepStatus): string => {
    switch (status) {
      case 'processing': return '#2196f3';
      case 'done': return '#4caf50';
      case 'error': return '#f44336';
      default: return '#666';
    }
  };

  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      });
    } catch {
      return timestamp;
    }
  };

  const renderStepData = (data: Record<string, any> | undefined) => {
    if (!data || Object.keys(data).length === 0) return null;

    return (
      <div className="step-data">
        {Object.entries(data).map(([key, value]) => (
          <div key={key} className="data-item">
            <span className="data-key">{key}:</span>
            <span className="data-value">
              {Array.isArray(value) ? value.join(', ') : String(value)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="workflow-chat loading">
        <div className="chat-header">
          <h3>🤖 AI Agent Workflow</h3>
          <div className="status-indicator loading">
            <div className="spinner"></div>
            <span>Loading...</span>
          </div>
        </div>
        <div className="chat-content">
          <div className="loading-message">
            <div className="spinner"></div>
            <span>Preparing workflow...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`workflow-chat ${isRunning ? 'running' : ''}`}>
      <div className="chat-header">
        <h3>🤖 AI Agent Workflow</h3>
        <div className={`status-indicator ${isRunning ? 'running' : 'idle'}`}>
          {isRunning ? (
            <>
              <div className="spinner"></div>
              <span>Running...</span>
            </>
          ) : (
            <>
              <div className="status-dot"></div>
              <span>Ready</span>
            </>
          )}
        </div>
      </div>

      <div className="chat-content">
        {steps.length === 0 && !isRunning ? (
          <div className="empty-chat">
            <div className="empty-icon">💬</div>
            <h4>Ready to start</h4>
            <p>Select documents and a template, then click "Start Workflow" to begin the AI agent process.</p>
          </div>
        ) : (
          <div className="steps-container">
            {steps.map((step, index) => (
              <div 
                key={step.step_id || index} 
                className={`step-message ${step.status}`}
              >
                <div className="step-header">
                  <div className="step-icon" style={{ color: getStepColor(step.status) }}>
                    {getStepIcon(step.status)}
                  </div>
                  <div className="step-info">
                    <div className="step-message-text">{step.message}</div>
                    <div className="step-timestamp">
                      {formatTimestamp(step.timestamp)}
                    </div>
                  </div>
                  {step.status === 'processing' && (
                    <div className="processing-spinner">
                      <div className="spinner small"></div>
                    </div>
                  )}
                </div>

                {step.data && renderStepData(step.data)}

                {step.error && (
                  <div className="step-error">
                    <strong>Error:</strong> {step.error}
                  </div>
                )}
              </div>
            ))}

            {isRunning && (
              <div className="typing-indicator">
                <div className="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span className="typing-text">AI agent is working...</span>
              </div>
            )}

            <div ref={chatEndRef} />
          </div>
        )}
      </div>

      {steps.length > 0 && !isRunning && (
        <div className="chat-footer">
          <div className="workflow-summary">
            <span>✅ Workflow completed with {steps.length} steps</span>
            {steps.some(s => s.data?.output_path) && (
              <button className="download-result-btn">
                💾 Download Result
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkflowChat;
