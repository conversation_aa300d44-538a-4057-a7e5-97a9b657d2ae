.file-uploader {
  width: 100%;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  position: relative;
  overflow: hidden;
}

.upload-area:hover:not(.disabled) {
  border-color: #646cff;
  background: #f8f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(100, 108, 255, 0.15);
}

.upload-area.drag-active {
  border-color: #646cff;
  background: #f0f2ff;
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(100, 108, 255, 0.2);
}

.upload-area.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 4rem;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
}

.upload-area:hover:not(.disabled) .upload-icon {
  transform: scale(1.1);
}

.upload-area.drag-active .upload-icon {
  transform: scale(1.2);
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0) scale(1.2);
  }
  40% {
    transform: translateY(-10px) scale(1.2);
  }
  80% {
    transform: translateY(-5px) scale(1.2);
  }
}

.upload-text {
  max-width: 400px;
}

.upload-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: #333;
  font-weight: 600;
}

.upload-text p {
  margin: 0.25rem 0;
  color: #666;
  font-size: 0.9rem;
}

.progress-container {
  width: 100%;
  max-width: 300px;
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #646cff, #4f46e5);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
}

.upload-hint {
  margin-top: 1rem;
  text-align: center;
}

.upload-hint small {
  color: #888;
  font-size: 0.85rem;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .upload-area {
    padding: 2rem 1rem;
  }
  
  .upload-icon {
    font-size: 3rem;
  }
  
  .upload-text h4 {
    font-size: 1.1rem;
  }
  
  .upload-text p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .upload-area {
    padding: 1.5rem 1rem;
  }
  
  .upload-icon {
    font-size: 2.5rem;
  }
  
  .upload-text h4 {
    font-size: 1rem;
  }
  
  .upload-text {
    max-width: 280px;
  }
}

/* Animation for successful upload */
.upload-area.success {
  border-color: #4caf50;
  background: #f1f8e9;
  animation: success-pulse 0.6s ease-in-out;
}

@keyframes success-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
