/**
 * Job Status Card Component
 * Displays the status and details of a document processing job.
 */

import React from 'react';
import { JobStatusProps, JobStatus } from '../types';
import './JobStatusCard.css';

const JobStatusCard: React.FC<JobStatusProps> = ({ job, onRefresh }) => {
  const getStatusColor = (status: JobStatus): string => {
    switch (status) {
      case 'uploading': return '#ff9800';
      case 'processing': return '#2196f3';
      case 'done': return '#4caf50';
      case 'failed': return '#f44336';
      default: return '#666';
    }
  };

  const getStatusIcon = (status: JobStatus): string => {
    switch (status) {
      case 'uploading': return '⬆️';
      case 'processing': return '⚙️';
      case 'done': return '✅';
      case 'failed': return '❌';
      default: return '❓';
    }
  };

  const getStatusText = (status: JobStatus): string => {
    switch (status) {
      case 'uploading': return 'Uploading';
      case 'processing': return 'Processing';
      case 'done': return 'Completed';
      case 'failed': return 'Failed';
      default: return 'Unknown';
    }
  };

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch {
      return dateString;
    }
  };

  const truncateText = (text: string, maxLength: number = 200): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const handleDownloadResult = () => {
    if (job.result) {
      const blob = new Blob([job.result], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${job.filename}_extracted.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const isProcessing = job.status === 'uploading' || job.status === 'processing';

  return (
    <div className={`job-status-card ${job.status}`}>
      <div className="card-header">
        <div className="status-indicator">
          <span 
            className="status-icon"
            style={{ color: getStatusColor(job.status) }}
          >
            {getStatusIcon(job.status)}
          </span>
          <span 
            className="status-text"
            style={{ color: getStatusColor(job.status) }}
          >
            {getStatusText(job.status)}
          </span>
        </div>
        
        {onRefresh && (
          <button 
            className="refresh-button"
            onClick={onRefresh}
            disabled={isProcessing}
            title="Refresh status"
          >
            🔄
          </button>
        )}
      </div>

      <div className="card-content">
        <div className="filename">
          <strong>📄 {job.filename}</strong>
        </div>
        
        <div className="job-details">
          <div className="detail-item">
            <span className="detail-label">Job ID:</span>
            <span className="detail-value job-id">{job.job_id}</span>
          </div>
          
          <div className="detail-item">
            <span className="detail-label">Created:</span>
            <span className="detail-value">{formatDate(job.created_at)}</span>
          </div>
        </div>

        {isProcessing && (
          <div className="processing-indicator">
            <div className="spinner"></div>
            <span>Processing document...</span>
          </div>
        )}

        {job.status === 'done' && job.result && (
          <div className="result-section">
            <div className="result-header">
              <span className="result-label">Extracted Text:</span>
              <button 
                className="download-button"
                onClick={handleDownloadResult}
                title="Download extracted text"
              >
                💾 Download
              </button>
            </div>
            <div className="result-preview">
              {truncateText(job.result)}
            </div>
          </div>
        )}

        {job.status === 'failed' && job.error && (
          <div className="error-section">
            <div className="error-header">
              <span className="error-label">Error:</span>
            </div>
            <div className="error-message">
              {job.error}
            </div>
          </div>
        )}
      </div>

      {isProcessing && (
        <div className="progress-bar">
          <div className="progress-fill"></div>
        </div>
      )}
    </div>
  );
};

export default JobStatusCard;
