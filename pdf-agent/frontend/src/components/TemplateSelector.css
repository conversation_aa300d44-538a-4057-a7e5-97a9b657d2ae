.template-selector {
  width: 100%;
}

.template-selector.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.template-selector.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.empty-state {
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.empty-state small {
  font-size: 0.85rem;
  color: #888;
}

.selector-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.selection-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.template-count {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.selected-indicator {
  font-size: 0.85rem;
  color: #2e7d32;
  font-weight: 600;
}

.templates-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
}

.template-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.template-item:last-child {
  border-bottom: none;
}

.template-item:hover:not(.disabled) {
  background: #f8f9fa;
}

.template-item.selected {
  background: #e8f5e9;
  border-left: 3px solid #4caf50;
}

.template-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.template-radio {
  position: relative;
  flex-shrink: 0;
}

.template-radio input[type="radio"] {
  opacity: 0;
  position: absolute;
  width: 18px;
  height: 18px;
  margin: 0;
}

.radio-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.template-item.selected .radio-custom {
  border-color: #4caf50;
}

.radio-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4caf50;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-meta {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  font-size: 0.8rem;
  color: #666;
}

.template-date, .template-size {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.template-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.ready {
  background: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.selection-summary {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f1f8e9;
  border-radius: 6px;
  border: 1px solid #c8e6c9;
}

.summary-content {
  display: flex;
  align-items: center;
}

.selected-template {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.selected-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.selected-info {
  flex: 1;
  min-width: 0;
}

.selected-name {
  font-weight: 600;
  color: #2e7d32;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.2rem;
}

.selected-meta {
  font-size: 0.8rem;
  color: #4caf50;
  font-style: italic;
}

.clear-selection-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-selection-btn:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.clear-selection-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Scrollbar styling */
.templates-list::-webkit-scrollbar {
  width: 6px;
}

.templates-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.templates-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.templates-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for selection */
.template-item.selected {
  animation: selectPulse 0.3s ease-out;
}

@keyframes selectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .templates-list {
    max-height: 250px;
  }
  
  .template-item {
    padding: 0.6rem;
    gap: 0.6rem;
  }
  
  .template-meta {
    font-size: 0.75rem;
  }
  
  .status-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .selection-summary {
    padding: 0.6rem;
  }
  
  .selected-name {
    font-size: 0.85rem;
  }
  
  .selected-meta {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .template-name {
    font-size: 0.85rem;
  }
  
  .template-meta {
    gap: 0.15rem;
  }
  
  .template-date, .template-size {
    font-size: 0.7rem;
  }
  
  .selected-template {
    gap: 0.5rem;
  }
  
  .selected-icon {
    font-size: 1.3rem;
  }
}
