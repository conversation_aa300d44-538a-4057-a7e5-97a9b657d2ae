.template-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #646cff;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.template-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.template-info {
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.template-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.85rem;
  color: #666;
}

.file-size {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.card-content {
  padding: 1.25rem;
}

.template-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.detail-label {
  color: #666;
  font-weight: 500;
}

.detail-value {
  color: #333;
  font-weight: 400;
}

.template-status {
  margin-bottom: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-indicator.ready {
  background: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.status-icon {
  font-size: 1rem;
}

.status-text {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0 1.25rem 1.25rem;
}

.action-btn {
  flex: 1;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-size: 0.85rem;
  color: #666;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.action-btn:hover {
  background: #e9e9e9;
  border-color: #ccc;
  color: #333;
}

.preview-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.download-btn:hover {
  background: #e8f5e9;
  border-color: #4caf50;
  color: #2e7d32;
}

.refresh-btn {
  flex: 0 0 auto;
  min-width: 40px;
}

.refresh-btn:hover {
  background: #fff3e0;
  border-color: #ff9800;
  color: #f57c00;
}

/* Animation for status indicator */
.status-indicator.ready .status-icon {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .card-header {
    padding: 1rem;
  }
  
  .template-icon {
    font-size: 2rem;
  }
  
  .template-name {
    font-size: 1rem;
  }
  
  .card-content {
    padding: 1rem;
  }
  
  .card-actions {
    padding: 0 1rem 1rem;
    flex-wrap: wrap;
  }
  
  .action-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 480px) {
  .template-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .action-btn {
    flex: none;
  }
  
  .refresh-btn {
    min-width: auto;
  }
}

/* Loading state */
.template-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.template-card.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Error state */
.template-card.error {
  border-color: #f44336;
  background: #ffebee;
}

.template-card.error .status-indicator {
  background: #ffebee;
  color: #c62828;
  border-color: #f44336;
}

.template-card.error .status-icon {
  animation: none;
}
