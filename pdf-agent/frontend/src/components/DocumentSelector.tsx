/**
 * Document Selector Component
 * Allows users to select multiple documents for the AI workflow.
 */

import React from 'react';
import { DocumentListItem } from '../types';
import './DocumentSelector.css';

interface DocumentSelectorProps {
  documents: DocumentListItem[];
  selectedDocuments: string[];
  onSelectionChange: (selected: string[]) => void;
  disabled?: boolean;
}

const DocumentSelector: React.FC<DocumentSelectorProps> = ({
  documents,
  selectedDocuments,
  onSelectionChange,
  disabled = false
}) => {
  const handleDocumentToggle = (jobId: string) => {
    if (disabled) return;

    const isSelected = selectedDocuments.includes(jobId);
    if (isSelected) {
      onSelectionChange(selectedDocuments.filter(id => id !== jobId));
    } else {
      onSelectionChange([...selectedDocuments, jobId]);
    }
  };

  const handleSelectAll = () => {
    if (disabled) return;

    if (selectedDocuments.length === documents.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(documents.map(doc => doc.job_id));
    }
  };

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch {
      return dateString;
    }
  };

  const truncateFilename = (filename: string, maxLength: number = 25): string => {
    if (filename.length <= maxLength) return filename;
    const extension = filename.split('.').pop();
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension!.length - 4);
    return `${truncatedName}...${extension}`;
  };

  if (documents.length === 0) {
    return (
      <div className="document-selector empty">
        <div className="empty-state">
          <div className="empty-icon">📄</div>
          <p>No processed documents available</p>
          <small>Upload and process documents first</small>
        </div>
      </div>
    );
  }

  return (
    <div className={`document-selector ${disabled ? 'disabled' : ''}`}>
      <div className="selector-header">
        <div className="selection-info">
          <span className="selected-count">
            {selectedDocuments.length} of {documents.length} selected
          </span>
        </div>
        <button
          className="select-all-btn"
          onClick={handleSelectAll}
          disabled={disabled}
        >
          {selectedDocuments.length === documents.length ? 'Deselect All' : 'Select All'}
        </button>
      </div>

      <div className="documents-list">
        {documents.map(document => {
          const isSelected = selectedDocuments.includes(document.job_id);
          
          return (
            <div
              key={document.job_id}
              className={`document-item ${isSelected ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
              onClick={() => handleDocumentToggle(document.job_id)}
            >
              <div className="document-checkbox">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => {}} // Handled by onClick
                  disabled={disabled}
                />
                <div className="checkbox-custom">
                  {isSelected && <span className="checkmark">✓</span>}
                </div>
              </div>

              <div className="document-info">
                <div className="document-name" title={document.filename}>
                  📄 {truncateFilename(document.filename)}
                </div>
                <div className="document-meta">
                  <span className="document-date">
                    {formatDate(document.created_at)}
                  </span>
                  <span className="document-status">
                    ✅ Processed
                  </span>
                </div>
              </div>

              {document.result && (
                <div className="document-preview">
                  <div className="preview-text">
                    {document.result.substring(0, 100)}
                    {document.result.length > 100 && '...'}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {selectedDocuments.length > 0 && (
        <div className="selection-summary">
          <div className="summary-header">
            <strong>Selected Documents:</strong>
          </div>
          <div className="selected-list">
            {selectedDocuments.map(jobId => {
              const doc = documents.find(d => d.job_id === jobId);
              return doc ? (
                <div key={jobId} className="selected-item">
                  <span className="selected-name">
                    {truncateFilename(doc.filename, 20)}
                  </span>
                  <button
                    className="remove-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDocumentToggle(jobId);
                    }}
                    disabled={disabled}
                    title="Remove from selection"
                  >
                    ×
                  </button>
                </div>
              ) : null;
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentSelector;
