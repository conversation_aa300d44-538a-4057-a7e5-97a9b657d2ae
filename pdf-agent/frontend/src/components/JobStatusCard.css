.job-status-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.job-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.job-status-card.processing {
  border-left: 4px solid #2196f3;
}

.job-status-card.uploading {
  border-left: 4px solid #ff9800;
}

.job-status-card.done {
  border-left: 4px solid #4caf50;
}

.job-status-card.failed {
  border-left: 4px solid #f44336;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-icon {
  font-size: 1.2rem;
}

.status-text {
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.refresh-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  font-size: 1rem;
  opacity: 0.7;
  transition: all 0.2s;
}

.refresh-button:hover:not(:disabled) {
  opacity: 1;
  background: rgba(0, 0, 0, 0.05);
}

.refresh-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.card-content {
  padding: 1.25rem;
}

.filename {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: #333;
  word-break: break-word;
}

.job-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.detail-label {
  color: #666;
  font-weight: 500;
}

.detail-value {
  color: #333;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.job-id {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.processing-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.result-section {
  margin-top: 1rem;
  border: 1px solid #e8f5e8;
  border-radius: 8px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f1f8e9;
  border-bottom: 1px solid #e8f5e8;
}

.result-label {
  font-weight: 600;
  color: #2e7d32;
  font-size: 0.9rem;
}

.download-button {
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.2s;
}

.download-button:hover {
  background: #45a049;
}

.result-preview {
  padding: 1rem;
  background: white;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #333;
  max-height: 150px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-section {
  margin-top: 1rem;
  border: 1px solid #ffebee;
  border-radius: 8px;
  overflow: hidden;
}

.error-header {
  padding: 0.75rem 1rem;
  background: #ffebee;
  border-bottom: 1px solid #ffcdd2;
}

.error-label {
  font-weight: 600;
  color: #c62828;
  font-size: 0.9rem;
}

.error-message {
  padding: 1rem;
  background: white;
  color: #d32f2f;
  font-size: 0.9rem;
  line-height: 1.4;
  word-break: break-word;
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #f0f0f0;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #1976d2);
  animation: progress-animation 2s ease-in-out infinite;
}

@keyframes progress-animation {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .card-header {
    padding: 0.75rem 1rem 0.5rem;
  }
  
  .card-content {
    padding: 1rem;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .job-id {
    max-width: none;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .download-button {
    align-self: flex-end;
  }
}
