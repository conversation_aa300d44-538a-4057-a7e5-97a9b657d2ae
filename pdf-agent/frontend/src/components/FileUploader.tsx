/**
 * File Uploader Component
 * Provides drag-and-drop file upload functionality with progress tracking.
 */

import React, { useState, useRef, DragEvent, ChangeEvent } from 'react';
import { FileUploaderProps } from '../types';
import './FileUploader.css';

const FileUploader: React.FC<FileUploaderProps> = ({
  accept,
  multiple = false,
  onUpload,
  uploading = false,
  disabled = false
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || uploading) return;

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFileInput = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFiles = (files: FileList) => {
    // Validate file types
    const validFiles: File[] = [];
    const acceptedTypes = accept.split(',').map(type => type.trim());
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (acceptedTypes.includes(fileExtension) || acceptedTypes.includes(file.type)) {
        validFiles.push(file);
      }
    }

    if (validFiles.length === 0) {
      alert(`Please select files with the following extensions: ${accept}`);
      return;
    }

    if (!multiple && validFiles.length > 1) {
      alert('Please select only one file');
      return;
    }

    // Create FileList from valid files
    const dt = new DataTransfer();
    validFiles.forEach(file => dt.items.add(file));
    
    onUpload(dt.files);
  };

  const handleClick = () => {
    if (!disabled && !uploading && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const formatFileTypes = (accept: string): string => {
    return accept
      .split(',')
      .map(type => type.trim().toUpperCase())
      .join(', ');
  };

  const getUploadIcon = (): string => {
    if (uploading) return '⏳';
    if (dragActive) return '📥';
    return '📁';
  };

  const getUploadText = (): string => {
    if (uploading) return 'Uploading...';
    if (dragActive) return 'Drop files here';
    return multiple ? 'Click to select files or drag and drop' : 'Click to select a file or drag and drop';
  };

  return (
    <div className="file-uploader">
      <div
        className={`upload-area ${dragActive ? 'drag-active' : ''} ${disabled || uploading ? 'disabled' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileInput}
          style={{ display: 'none' }}
          disabled={disabled || uploading}
        />

        <div className="upload-content">
          <div className="upload-icon">
            {getUploadIcon()}
          </div>
          
          <div className="upload-text">
            <h4>{getUploadText()}</h4>
            <p>Supported formats: {formatFileTypes(accept)}</p>
            <p>Maximum file size: 50MB</p>
          </div>

          {uploading && uploadProgress > 0 && (
            <div className="progress-container">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <span className="progress-text">{uploadProgress}%</span>
            </div>
          )}
        </div>
      </div>

      {!multiple && (
        <div className="upload-hint">
          <small>💡 Tip: You can also drag and drop files directly onto this area</small>
        </div>
      )}

      {multiple && (
        <div className="upload-hint">
          <small>💡 Tip: You can select multiple files at once or drag and drop them</small>
        </div>
      )}
    </div>
  );
};

export default FileUploader;
