/**
 * WebSocket client for real-time communication with the PDF Agent backend.
 * Handles workflow streaming and connection management.
 */

import { WebSocketClient, WebSocketMessage, WorkflowStep } from '../types';

class PDFAgentWebSocketClient implements WebSocketClient {
  private ws: WebSocket | null = null;
  private baseURL: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private messageCallbacks: ((message: WebSocketMessage) => void)[] = [];
  private errorCallbacks: ((error: Event) => void)[] = [];
  private closeCallbacks: ((event: CloseEvent) => void)[] = [];

  constructor(baseURL: string = 'ws://localhost:8000') {
    this.baseURL = baseURL.replace('http://', 'ws://').replace('https://', 'wss://');
  }

  connect(workflowId: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.warn('WebSocket already connected');
      return;
    }

    const wsURL = `${this.baseURL}/ws/agent-workflow/${workflowId}`;
    console.log('Connecting to WebSocket:', wsURL);

    try {
      this.ws = new WebSocket(wsURL);

      this.ws.onopen = (event) => {
        console.log('WebSocket connected:', event);
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('WebSocket message received:', data);

          // Convert backend message format to our WebSocket message format
          let message: WebSocketMessage;

          if (data.error) {
            message = {
              type: 'error',
              data: { error: data.error }
            };
          } else if (data.step_id) {
            // This is a workflow step
            message = {
              type: 'workflow_step',
              data: data as WorkflowStep
            };
          } else if (data.workflow_id) {
            message = {
              type: 'complete',
              data: { workflow_id: data.workflow_id }
            };
          } else {
            // Default to workflow step format
            message = {
              type: 'workflow_step',
              data: data as WorkflowStep
            };
          }

          this.messageCallbacks.forEach(callback => callback(message));
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          const errorMessage: WebSocketMessage = {
            type: 'error',
            data: { error: 'Failed to parse message from server' }
          };
          this.messageCallbacks.forEach(callback => callback(errorMessage));
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.errorCallbacks.forEach(callback => callback(error));
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket closed:', event);
        this.closeCallbacks.forEach(callback => callback(event));

        // Attempt to reconnect if not closed intentionally
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.attemptReconnect(workflowId);
        }
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.errorCallbacks.forEach(callback => callback(error as Event));
    }
  }

  disconnect(): void {
    if (this.ws) {
      console.log('Disconnecting WebSocket');
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
  }

  send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('Sending WebSocket message:', message);
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message:', message);
      throw new Error('WebSocket not connected');
    }
  }

  onMessage(callback: (message: WebSocketMessage) => void): void {
    this.messageCallbacks.push(callback);
  }

  onError(callback: (error: Event) => void): void {
    this.errorCallbacks.push(callback);
  }

  onClose(callback: (event: CloseEvent) => void): void {
    this.closeCallbacks.push(callback);
  }

  // Remove callbacks
  removeMessageCallback(callback: (message: WebSocketMessage) => void): void {
    const index = this.messageCallbacks.indexOf(callback);
    if (index > -1) {
      this.messageCallbacks.splice(index, 1);
    }
  }

  removeErrorCallback(callback: (error: Event) => void): void {
    const index = this.errorCallbacks.indexOf(callback);
    if (index > -1) {
      this.errorCallbacks.splice(index, 1);
    }
  }

  removeCloseCallback(callback: (event: CloseEvent) => void): void {
    const index = this.closeCallbacks.indexOf(callback);
    if (index > -1) {
      this.closeCallbacks.splice(index, 1);
    }
  }

  // Clear all callbacks
  clearCallbacks(): void {
    this.messageCallbacks = [];
    this.errorCallbacks = [];
    this.closeCallbacks = [];
  }

  // Get connection state
  getReadyState(): number {
    return this.ws ? this.ws.readyState : WebSocket.CLOSED;
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  private attemptReconnect(workflowId: string): void {
    this.reconnectAttempts++;
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

    setTimeout(() => {
      this.connect(workflowId);
    }, this.reconnectDelay);

    // Exponential backoff
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000); // Max 30 seconds
  }

  // Update base URL if needed
  updateBaseURL(baseURL: string): void {
    this.baseURL = baseURL.replace('http://', 'ws://').replace('https://', 'wss://');
  }
}

// Create and export a singleton instance
export const wsClient = new PDFAgentWebSocketClient();

// Export the class for testing or custom instances
export { PDFAgentWebSocketClient };

// Utility hook for React components (to be used with useEffect)
export const useWebSocket = (
  workflowId: string | null,
  onMessage: (message: WebSocketMessage) => void,
  onError?: (error: Event) => void,
  onClose?: (event: CloseEvent) => void
) => {
  const connect = () => {
    if (workflowId) {
      wsClient.onMessage(onMessage);
      if (onError) wsClient.onError(onError);
      if (onClose) wsClient.onClose(onClose);
      wsClient.connect(workflowId);
    }
  };

  const disconnect = () => {
    wsClient.removeMessageCallback(onMessage);
    if (onError) wsClient.removeErrorCallback(onError);
    if (onClose) wsClient.removeCloseCallback(onClose);
    wsClient.disconnect();
  };

  return { connect, disconnect, send: wsClient.send.bind(wsClient) };
};
