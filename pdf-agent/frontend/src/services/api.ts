/**
 * API client for communicating with the PDF Agent backend.
 * Handles all REST API calls and error handling.
 */

import axios, { AxiosInstance, AxiosError } from 'axios';
import {
  APIClient,
  DocumentUploadResponse,
  JobStatusResponse,
  DocumentListItem,
  TemplateUploadResponse,
  TemplateListItem,
  AgentWorkflowRequest,
  AgentWorkflowResponse,
  HealthCheckResponse,
  APIError
} from '../types';

class PDFAgentAPIClient implements APIClient {
  private client: AxiosInstance;

  constructor(baseURL: string = 'http://localhost:8000') {
    this.client = axios.create({
      baseURL,
      timeout: 30000, // 30 seconds
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        console.error('API Error:', error);
        
        if (error.response?.data) {
          // Backend returned an error response
          throw new Error((error.response.data as APIError).error || error.message);
        } else if (error.request) {
          // Network error
          throw new Error('Network error: Unable to connect to server');
        } else {
          // Other error
          throw new Error(error.message || 'Unknown error occurred');
        }
      }
    );
  }

  async uploadDocument(file: File): Promise<DocumentUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post<DocumentUploadResponse>(
      '/api/documents',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  }

  async getDocumentStatus(jobId: string): Promise<JobStatusResponse> {
    const response = await this.client.get<JobStatusResponse>(`/api/documents/${jobId}`);
    return response.data;
  }

  async listDocuments(): Promise<DocumentListItem[]> {
    const response = await this.client.get<DocumentListItem[]>('/api/documents');
    return response.data;
  }

  async uploadTemplate(file: File): Promise<TemplateUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post<TemplateUploadResponse>(
      '/api/templates',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  }

  async listTemplates(): Promise<TemplateListItem[]> {
    const response = await this.client.get<{ templates: TemplateListItem[] }>('/api/templates');
    return response.data.templates;
  }

  async startWorkflow(request: AgentWorkflowRequest): Promise<AgentWorkflowResponse> {
    const response = await this.client.post<AgentWorkflowResponse>(
      '/api/agent-workflow',
      request
    );
    return response.data;
  }

  async healthCheck(): Promise<HealthCheckResponse> {
    const response = await this.client.get<HealthCheckResponse>('/api/health');
    return response.data;
  }

  // Utility method to check if the backend is available
  async isBackendAvailable(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch {
      return false;
    }
  }

  // Method to update base URL if needed
  updateBaseURL(baseURL: string): void {
    this.client.defaults.baseURL = baseURL;
  }
}

// Create and export a singleton instance
export const apiClient = new PDFAgentAPIClient();

// Export the class for testing or custom instances
export { PDFAgentAPIClient };

// Utility function for handling file upload with progress
export const uploadFileWithProgress = async (
  file: File,
  uploadFn: (file: File) => Promise<any>,
  onProgress?: (progress: number) => void
): Promise<any> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('file', file);

    const xhr = new XMLHttpRequest();

    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });
    }

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (e) {
          reject(new Error('Invalid response format'));
        }
      } else {
        try {
          const error = JSON.parse(xhr.responseText);
          reject(new Error(error.error || `HTTP ${xhr.status}`));
        } catch (e) {
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
        }
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Network error'));
    });

    xhr.addEventListener('timeout', () => {
      reject(new Error('Upload timeout'));
    });

    xhr.timeout = 30000; // 30 seconds

    // Determine the endpoint based on file type
    const endpoint = file.type === 'application/pdf' && file.name.includes('template') 
      ? '/api/templates' 
      : '/api/documents';

    xhr.open('POST', `${apiClient['client'].defaults.baseURL}${endpoint}`);
    xhr.send(formData);
  });
};
